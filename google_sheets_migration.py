# Google Sheets Data Migration Script
# Install required packages
!pip install gspread gradio pandas

import gspread
from google.colab import auth
import pandas as pd
import gradio as gr
from datetime import datetime, timedelta
from collections import defaultdict
import json
import re
import time

# Global variables for progress tracking
progress_percentage = 0
processing_logs = []
current_status = "Ready"

def authenticate_google():
    """Authenticate with Google Sheets API"""
    try:
        auth.authenticate_user()
        gc = gspread.service_account()
        return gc
    except Exception as e:
        return None

def parse_date(date_str):
    """Parse date from DD.MM.YY or DD.MM.YYYY format"""
    if not date_str or pd.isna(date_str):
        return None
    
    date_str = str(date_str).strip()
    formats = ['%d.%m.%y', '%d.%m.%Y']
    
    for fmt in formats:
        try:
            return datetime.strptime(date_str, fmt)
        except:
            continue
    return None

def parse_amount(amount_str):
    """Parse amount removing $ and commas"""
    if not amount_str or pd.isna(amount_str):
        return 0
    
    amount_str = str(amount_str).replace('$', '').replace(',', '').strip()
    try:
        return float(amount_str)
    except:
        return 0

def calculate_next_email_date(recent_date, frequency_text):
    """Calculate next email date based on frequency"""
    if not recent_date:
        return None
    
    if "DAILY" in frequency_text.upper():
        return recent_date + timedelta(days=1)
    elif "WEEK" in frequency_text.upper():
        return recent_date + timedelta(days=7)
    return None

def update_progress(percentage, message):
    """Update global progress variables"""
    global progress_percentage, processing_logs, current_status
    progress_percentage = percentage
    current_status = message
    timestamp = datetime.now().strftime("%H:%M:%S")
    processing_logs.append(f"[{timestamp}] {message}")

def process_spreadsheet(source_id, target_id):
    """Main processing function"""
    global progress_percentage, processing_logs, current_status
    
    # Reset globals
    progress_percentage = 0
    processing_logs = []
    current_status = "Starting..."
    
    try:
        # Authenticate
        update_progress(5, "🔐 Authenticating with Google...")
        gc = authenticate_google()
        if not gc:
            return "❌ Authentication failed", "", ""
        
        # Open spreadsheets
        update_progress(10, "📊 Opening source spreadsheet...")
        source_sheet = gc.open_by_key(source_id)
        
        update_progress(15, "📋 Opening target spreadsheet...")
        target_sheet = gc.open_by_key(target_id)
        
        # Process data
        all_data = []
        company_stats = defaultdict(lambda: {'count': 0, 'total_amount': 0, 'emails': 0})
        
        worksheets = source_sheet.worksheets()
        total_sheets = len(worksheets)
        
        for i, worksheet in enumerate(worksheets):
            sheet_progress = 20 + (i / total_sheets) * 50
            update_progress(sheet_progress, f"📄 Processing sheet: {worksheet.title}")
            
            try:
                # Get all values
                values = worksheet.get_all_values()
                if len(values) < 3:
                    continue
                
                # Extract frequency from row 1
                frequency_text = values[0][0] if values[0] else ""
                
                # Process data rows (starting from row 3, index 2)
                for row_idx, row in enumerate(values[2:], start=3):
                    if len(row) < 4 or not any(row[:4]):
                        continue
                    
                    name = row[0].upper().strip() if row[0] else ""
                    email = row[1].strip() if row[1] else ""
                    date_str = row[2].strip() if row[2] else ""
                    amount_str = row[3].strip() if row[3] else ""
                    
                    if not name or not email:
                        continue
                    
                    # Parse dates and amounts
                    dates = [parse_date(d.strip()) for d in date_str.split(',') if d.strip()]
                    dates = [d for d in dates if d]
                    amount = parse_amount(amount_str)
                    
                    if dates:
                        first_date = min(dates)
                        recent_date = max(dates)
                        next_date = calculate_next_email_date(recent_date, frequency_text)
                        
                        # Update next email date in source (column H)
                        if next_date:
                            cell_address = f"H{row_idx}"
                            worksheet.update(cell_address, next_date.strftime("%d.%m.%Y"))
                        
                        # Add to processed data
                        company = worksheet.title
                        all_data.append({
                            'Company': company,
                            'Person': name,
                            'Email': email,
                            'Total Emails': len(dates),
                            'First Email': first_date.strftime("%d.%m.%Y"),
                            'Recent Email': recent_date.strftime("%d.%m.%Y"),
                            'Amount ($)': amount
                        })
                        
                        # Update company stats
                        company_stats[company]['count'] += 1
                        company_stats[company]['total_amount'] += amount
                        company_stats[company]['emails'] += len(dates)
            
            except Exception as e:
                update_progress(sheet_progress, f"⚠️ Error in sheet {worksheet.title}: {str(e)}")
                continue
        
        # Create target worksheets
        update_progress(75, "📝 Creating WD_Email_Details worksheet...")
        
        # Remove default Sheet1 if exists
        try:
            default_sheet = target_sheet.worksheet('Sheet1')
            target_sheet.del_worksheet(default_sheet)
        except:
            pass
        
        # Create main data worksheet
        try:
            details_ws = target_sheet.worksheet('WD_Email_Details')
            target_sheet.del_worksheet(details_ws)
        except:
            pass
        
        details_ws = target_sheet.add_worksheet('WD_Email_Details', rows=len(all_data)+10, cols=7)
        
        # Add headers
        headers = ['Company', 'Person', 'Email', 'Total Emails', 'First Email', 'Recent Email', 'Amount ($)']
        details_ws.update('A1:G1', [headers])
        
        # Add data
        if all_data:
            data_rows = [[row[col] for col in headers] for row in all_data]
            details_ws.update(f'A2:G{len(all_data)+1}', data_rows)
        
        # Format worksheet
        update_progress(85, "🎨 Applying formatting...")
        
        # Header formatting
        details_ws.format('A1:G1', {
            'backgroundColor': {'red': 0.2, 'green': 0.6, 'blue': 0.9},
            'textFormat': {'bold': True, 'foregroundColor': {'red': 1, 'green': 1, 'blue': 1}}
        })
        
        # Currency formatting for amount column
        if len(all_data) > 0:
            details_ws.format(f'G2:G{len(all_data)+1}', {'numberFormat': {'type': 'CURRENCY'}})
        
        # Auto-resize columns
        details_ws.columns_auto_resize(0, 6)
        
        # Create Summary worksheet
        update_progress(90, "📊 Creating Summary worksheet...")
        
        try:
            summary_ws = target_sheet.worksheet('Summary')
            target_sheet.del_worksheet(summary_ws)
        except:
            pass
        
        summary_ws = target_sheet.add_worksheet('Summary', rows=20, cols=5)
        
        # Summary data
        summary_data = [
            ['Company Statistics', '', '', '', ''],
            ['Company', 'People Count', 'Total Emails', 'Total Amount ($)', ''],
        ]
        
        for company, stats in company_stats.items():
            summary_data.append([
                company, 
                stats['count'], 
                stats['emails'], 
                stats['total_amount'],
                ''
            ])
        
        summary_ws.update('A1:E' + str(len(summary_data)), summary_data)
        
        # Format summary
        summary_ws.format('A1:E1', {
            'backgroundColor': {'red': 0.9, 'green': 0.6, 'blue': 0.2},
            'textFormat': {'bold': True, 'fontSize': 14}
        })
        
        summary_ws.format('A2:E2', {
            'backgroundColor': {'red': 0.8, 'green': 0.8, 'blue': 0.8},
            'textFormat': {'bold': True}
        })
        
        # Currency formatting for summary amounts
        if len(company_stats) > 0:
            summary_ws.format(f'D3:D{len(company_stats)+2}', {'numberFormat': {'type': 'CURRENCY'}})
        
        summary_ws.columns_auto_resize(0, 4)
        
        update_progress(100, "✅ Processing completed successfully!")
        
        # Generate summary HTML
        total_records = len(all_data)
        total_companies = len(company_stats)
        total_amount = sum(stats['total_amount'] for stats in company_stats.values())
        
        summary_html = f"""
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 20px; border-radius: 10px; color: white; margin: 10px 0;">
            <h3>📊 Processing Summary</h3>
            <p><strong>Total Records:</strong> {total_records}</p>
            <p><strong>Companies Processed:</strong> {total_companies}</p>
            <p><strong>Total Amount:</strong> ${total_amount:,.2f}</p>
            <p><strong>Target Spreadsheet:</strong> <a href="https://docs.google.com/spreadsheets/d/{target_id}" target="_blank" style="color: #FFD700;">Open Spreadsheet</a></p>
        </div>
        """
        
        # Company details table
        company_table = "<table style='width:100%; border-collapse: collapse; margin-top: 10px;'>"
        company_table += "<tr style='background-color: #f2f2f2;'><th style='border: 1px solid #ddd; padding: 8px;'>Company</th><th style='border: 1px solid #ddd; padding: 8px;'>People</th><th style='border: 1px solid #ddd; padding: 8px;'>Emails</th><th style='border: 1px solid #ddd; padding: 8px;'>Amount</th></tr>"
        
        for company, stats in company_stats.items():
            company_table += f"<tr><td style='border: 1px solid #ddd; padding: 8px;'>{company}</td><td style='border: 1px solid #ddd; padding: 8px;'>{stats['count']}</td><td style='border: 1px solid #ddd; padding: 8px;'>{stats['emails']}</td><td style='border: 1px solid #ddd; padding: 8px;'>${stats['total_amount']:,.2f}</td></tr>"
        
        company_table += "</table>"
        
        return "✅ Success", summary_html, company_table
        
    except Exception as e:
        error_msg = f"❌ Error: {str(e)}"
        update_progress(0, error_msg)
        return error_msg, "", ""

def get_progress():
    """Return current progress information"""
    global progress_percentage, processing_logs, current_status
    
    logs_text = "\n".join(processing_logs[-10:])  # Show last 10 logs
    
    return progress_percentage, current_status, logs_text

# Custom CSS for modern styling
css = """
.gradio-container {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.gr-button {
    background: linear-gradient(45deg, #FE6B8B 30%, #FF8E53 90%);
    border: none;
    border-radius: 25px;
    color: white;
    font-weight: bold;
    transition: all 0.3s ease;
}

.gr-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
}

.gr-textbox {
    border-radius: 10px;
    border: 2px solid #ddd;
    transition: border-color 0.3s ease;
}

.gr-textbox:focus {
    border-color: #667eea;
    box-shadow: 0 0 10px rgba(102, 126, 234, 0.3);
}

.status-box {
    background: rgba(255,255,255,0.1);
    border-radius: 10px;
    padding: 15px;
    margin: 10px 0;
    font-family: 'Courier New', monospace;
    color: white;
}
"""

# Create Gradio interface
with gr.Blocks(css=css, title="Google Sheets Migration Tool") as demo:
    gr.HTML("""
    <div style="text-align: center; padding: 20px; background: rgba(255,255,255,0.1); border-radius: 15px; margin-bottom: 20px;">
        <h1 style="color: white; margin: 0; font-size: 2.5em;">📊 Google Sheets Migration Tool</h1>
        <p style="color: rgba(255,255,255,0.8); font-size: 1.2em; margin: 10px 0 0 0;">Professional data migration with real-time progress tracking</p>
    </div>
    """)
    
    with gr.Row():
        with gr.Column():
            source_id = gr.Textbox(
                label="📥 Source Spreadsheet ID",
                placeholder="Enter the Google Sheets ID of your source spreadsheet",
                lines=1
            )
            target_id = gr.Textbox(
                label="📤 Target Spreadsheet ID", 
                placeholder="Enter the Google Sheets ID of your target spreadsheet",
                lines=1
            )
            
            process_btn = gr.Button("🚀 Start Migration", variant="primary", size="lg")
    
    with gr.Row():
        with gr.Column():
            progress_bar = gr.Slider(
                minimum=0, maximum=100, value=0, 
                label="📈 Progress", 
                interactive=False
            )
            status_text = gr.Textbox(
                label="📋 Current Status", 
                value="Ready to start...",
                interactive=False
            )
            
        with gr.Column():
            logs_text = gr.Textbox(
                label="📝 Processing Logs",
                lines=8,
                interactive=False,
                elem_classes=["status-box"]
            )
    
    result_status = gr.Textbox(label="🎯 Result", interactive=False)
    summary_html = gr.HTML()
    company_table = gr.HTML()
    
    # Event handlers
    process_btn.click(
        fn=process_spreadsheet,
        inputs=[source_id, target_id],
        outputs=[result_status, summary_html, company_table]
    )
    
    # Auto-refresh progress every 2 seconds
    demo.load(
        fn=get_progress,
        outputs=[progress_bar, status_text, logs_text],
        every=2
    )

# Launch the interface
if __name__ == "__main__":
    demo.launch(share=True, debug=True)